package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

public class CaseReasonMatcher extends UDF {


    //    private static Map<String, List<CaseReasonMapping>> groupedMappings = new HashMap<>();
    private static List<CaseReasonMapping> sorted = new ArrayList<>();

    static {
        try (InputStream is = CaseReasonMatcher.class.getResourceAsStream("/case_reason_20250716.csv")) {
            BufferedReader br = new BufferedReader(new InputStreamReader(is, "gbk"));
            String line;
            while ((line = br.readLine()) != null) {
                String[] parts = line.split(",", -1);
                if (parts.length >= 3 && StringUtils.isNotEmpty(parts[1])) {
                    if (parts[0].trim().equals("侵害商标权") && parts[1].trim().equals("商标权权属、侵权纠纷")) {
                        continue;
                    }
                    sorted.add(new CaseReasonMapping(parts[0].trim(), parts[1].trim(), parts[2].trim()));
                }
            }
            // ✅ 分组后，按同组内原始案由长度倒序排列
//            groupedMappings = sorted.stream()
//                    .collect(Collectors.groupingBy(m -> m.standardReason));

//            for (List<CaseReasonMapping> group : groupedMappings.values()) {
            sorted.sort(Comparator.comparingInt((CaseReasonMapping m) -> m.originalReason.length()).reversed());
//            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    // ✅ 每个标准案由匹配一次即可，匹配顺序：正文 > 标题 > 爬虫案由
    public String evaluate(String casereason, String title, String contentText) {
        Set<String> matchedContent = new LinkedHashSet<>();
        Set<String> matchedTitle = new LinkedHashSet<>();
        Set<String> matched = new LinkedHashSet<>();

//        for (Map.Entry<String, List<CaseReasonMapping>> entry : groupedMappings.entrySet()) {
//            String standardReason = entry.getKey();
//            List<CaseReasonMapping> group = entry.getValue();
        List<CaseReasonMapping> group = sorted;

        String hetongjiufen = "";
        boolean matchedThisGroup = false;

        // 优先级1：正文
        for (CaseReasonMapping m : group) {
            if (m.originalReason.length() < 3) {
                continue;
            }
            if (contentText != null && contentText.contains(m.originalReason)) {
                matchedContent.add(m.standardReason);
                hetongjiufen = m.standardReason;
                if (!m.standardReason.equals("合同纠纷")) {
                    return m.standardReason;
                }

//                    matchedThisGroup = true;
//                    break;
            }
        }

//            if (matchedThisGroup) continue;

        // 优先级2：标题
        for (CaseReasonMapping m : group) {
            if (title != null && title.contains(m.originalReason)) {
                matchedTitle.add(m.standardReason);
                matchedThisGroup = true;
                if (hetongjiufen.equals("合同纠纷")) {
                    if (m.standardReason.length() > hetongjiufen.length()) {
                        return m.standardReason;
                    }
                } else {
                    return m.standardReason;
                }
//                    break;
            }
        }

//            if (matchedThisGroup) continue;

        // 优先级3：爬虫案由
        for (CaseReasonMapping m : group) {
            if (casereason != null && casereason.contains(m.originalReason)) {
                matched.add(m.standardReason);
                if (hetongjiufen.equals("合同纠纷")) {
                    if (m.standardReason.length() > hetongjiufen.length()) {
                        return m.standardReason;
                    }
                } else {
                    return m.standardReason;
                }
                //                    break;
            }
        }
//        }
        if (hetongjiufen.equals("合同纠纷")) {
            return hetongjiufen;
        }

        if (matched.isEmpty() && matchedContent.isEmpty() && matchedTitle.isEmpty()) {
            matched.add("兜底案由");
            return "兜底案由";
        }
        if (CollectionUtils.isNotEmpty(matchedContent)) {
            return JSON.toJSONString(matchedContent);
        }
        if (CollectionUtils.isNotEmpty(matchedTitle)) {
            return JSON.toJSONString(matchedTitle);
        }
        return JSON.toJSONString(matched);
    }

    public static class CaseReasonMapping {
        public String originalReason;
        public String standardReason;
        public String reasonCode;

        public CaseReasonMapping(String originalReason, String standardReason, String reasonCode) {
            this.originalReason = originalReason;
            this.standardReason = standardReason;
            this.reasonCode = reasonCode;
        }
    }


    public static void main(String[] args) throws Exception {
        CaseReasonMatcher matcher = new CaseReasonMatcher();


        String casereason = "[\"罚金\"]";  // 爬虫字段
        String title = "周传龙首次执行执行裁定书";
        String content = "移送单位：仁寿县人民法院。 被执行人：周传龙,男性,****年**月**日出生,汉族，住**。,本院于2020年12月25日作出的（2020）川1421刑初381号刑事判决书并处判周传龙罚金5000元发生法律效力后，被执行人周传龙未按生效法律文书确定的内容履行缴纳罚金的义务。本院于2021年8月10日立案执行后，在执行中，本院依法向被执行人送达了执行通知书、报告财产令、限制消费令。2021年10月28日本院作出（2021）川1421执2553号执行裁定书，裁定：查封被执行人周传龙与案外人邱作祥、胡建琼、胡银楷共同共有的位于简阳市地（不动产权证号：川（2018）简阳市不动产权第0072849），期限为三年。因上述房产系共有财产，无法确定份额，故暂无法进行处置。经全国执行网络查询系统对被执行人的银行存款、互联网银行、不动产、车辆、保险、工商登记等财产信息进行了查询，依法冻结了被执行人银行账号，依法扣划被执行人银行存款4105元，未发现可供执行财产。本院另向仁寿县不动产登记中心、仁寿县综治网格中心对被执行人的下落及财产情况进行调查，均未发现可供执行的财产线索。本院依法将被执行人限制高消费。现鉴于被执行人暂无可供执行的财产，本院决定依职权终结（2020）川1421刑初381号刑事判决书并处判周传龙罚金895元财产刑的本次执行程序。被执行人尚未履行生效法律文书确定的罚金895元财产刑全部义务。依照《中华人民共和国民事诉讼法》第二百五十七条、《最高人民法院关于适用〈中华人民共和国民事诉讼法〉的解释》第五百一十九条之规定，裁定如下：";

        String result = matcher.evaluate(casereason, title, content);
        System.out.println("匹配标准案由：" + result);


        String casereason2 = "";  // 爬虫字段
        String title2 = "李某某合同、无因管理、不当得利纠纷执行实施类执行裁定书";
        String content2 = "被执行人：李某某，男性，1965年06月20出生，汉族，住**。,本院在执行与李某某罚金一案中，依据已经发生法律效力的葫芦岛市南票区人民法院（2019）辽1404刑初151号判决书，已向被执行人李某某发出执行通知书，责令被执行人接到执行通知后立即履行该法律文书确定的义务，但被执行人李某某未按执行通知履行法律文书确定的义务，依照《中华人民共和国民事诉讼法》第一百五十四条第一款第（十一）项、第二百四十条、第二百四十二条、第二百四十三条、第二百四十四条、第二百四十七条以及第二百五十三条之规定，裁定如下：";

        String result2 = matcher.evaluate(casereason2, title2, content2);
        System.out.println("匹配标准案由：" + result2);


        String casereason3 = "";  // 爬虫字段
        String title3 = "郑向生非法吸收公众存款案处置工作领导小组、郑向生储蓄存款合同纠纷执行实施类执行裁定书";
        String content3 = "申请人：郑向生非法吸收公众存款案处置工作领导小组。住所地：铜鼓县烈士陵园旁原劳动人事局大楼。 联系人：冷思敏。 被执行人：郑向生，男，****年**月**日出生，汉族，铜鼓县人，住**。 被执行人：郑永金，男，****年**月**日出生，汉族，铜鼓县人，住**。,本院在执行申请执行人郑向生非法吸收公众存款案处置工作领导小组与被执行人郑向生、郑永金（下称被执行人）非法吸收公众存款纠纷系列案中，依据发生法律效力的本院（2015）铜刑初字第56号刑事判决书，上述被执行人须退赔该案刑事受害人尚未返还资金。因被执行人未履行生效法律文书确定的义务，本院于2018年10月24日至11月20日期间两次挂网拍卖实际由被执行人郑向生开发的位于铜鼓县永宁镇城南东路“子龙府”1号楼150套住房，现已成功拍卖住房6套（11-1-03、4-2-01、23-2-01及24-2-01、12-2-04、16-2-03），其余144套均已流拍。经我院征询申请人郑向生非法吸收公众存款案处置工作领导小组意见，申请人同意上述144套房产以二拍流拍价（即评估价）以物抵债至其后，由其依法处置再行分配给该案债权人。依据《最高人民法院关于人民法院民事执行中拍卖、变卖财产的规定》第十九条、第二十三条、第二十七条、第二十八条、第二十九条之规定，裁定如下：";

        String result3 = matcher.evaluate(casereason3, title3, content3);
        System.out.println("匹配标准案由：" + result3);


        String casereason4 = "[\"行纪合同纠纷\"]";  // 爬虫字段
        String title4 = "青海安意房地产中介服务有限公司、常立娟中介合同纠纷民事一审民事裁定书";
        String content4 = "原告：青海安意房地产中介服务有限公司，住所：青海省西宁市城北区柴达木路351号16号楼1单元1011室。 法定代表人：雷菊林，该公司总经理。 被告：常立娟，女，****年**月**日出生，汉族，住**。 委托诉讼代理人：宋长银，男，****年**月**日出生，汉族，住青海省西宁市城北区。,原告青海安意房地产中介服务有限公司与被告常立娟中介合同纠纷一案，本院于2023年4月11日立案。原告青海安意房地产中介服务有限公司于2023年5月19日向本院提出撤诉申请。";

        String result4 = matcher.evaluate(casereason4, title4, content4);
        System.out.println("匹配标准案由：" + result4);

        String casereason5 = "";  // 爬虫字段
        String title5 = "申请人株洲市天元区人民法院刑事审判庭与被执行人李志周责令退赔纠纷一案终本裁定书";
        String content5 = "申请执行人：株洲市天元区人民法院刑事审判庭。 被执行人：李志周，男，****年**月**日出生，汉族。 申请人株洲市天元区人民法院,刑事审判庭与被执行人李志周责令退赔纠纷一案，本院作出（2019）湘0211刑初43号刑事判决书，已发生法律效力。由于被执行人未能按期履行生效法律文书确定的义务，本院于2019年8月12日立案执行。 2019年8月21日，本院向被执行人李志周邮寄送达了执行通知书、报告财产令，于2019年8月12日向申请执行人送达受理通知书并要求其提供财产线索。于2019年8月13日发起全国法院执行查控系统，未发现任何有可供执行的财产。于2019年9月6日向株洲市住房公积金管理中心查询被执行人公积金账户，同日向中国人寿股份有限公司株洲分公司查询被执行人收益类保险情况。于2019年9月10日向不动产登记中心查询被执行人的不动产和其他财产情况，均未发现可供执行财产。 2019年9月18日本院对被执行人李志周适用限制高消费措施，并在中国执行信息公开网上公布。 上述执行情况及信息，本院于2019年9月18日通过谈话告知了申请执行人，并要求其提供其他财产线索，但申请执行人未能提供。本院同时依法告知了申请执行人，本案将做终结本次执行程序结案。 本案认为，截止2019年9月18日，本案应当执行标的金额为79.2570万元，尚有债权79.2570万元未执行到位。经本院采取上述执行行为，目前并未发现被执行人有可供执行且适宜处置的财产。申请执行人也未能提供其他可供执行的财产线索。依照《最高人民法院关于适用〈中华人民共和国民事诉讼法〉的解释》第五百一十九条之规定，裁定如下：";

        String result5 = matcher.evaluate(casereason5, title5, content5);
        System.out.println("匹配标准案由：" + result5);

        String casereason6 = "[\"侵害商标权纠纷\"]";  // 爬虫字段
        String title6 = "欧普某公司与朱某侵害商标权纠纷一审民事裁定书";
        String content6 = "原告：欧普照明股份有限公司，住所地上海市浦东新区龙东大道6111号1幢411室，统一社会信用代码91310000680999558Q。 法定代表人:王耀海，该公司董事长。 委托诉讼代理人：翟明跃，山东昌平律师事务所律师。 被告：朱绍强，男，****年**月**日出生，汉族，住**。,原告欧普照明股份有限公司与被告朱绍强侵害商标权纠纷一案，本院于2024年4月10日立案。原告欧普照明股份有限公司于2024年6月27日向本院申请撤诉。";

        String result6 = matcher.evaluate(casereason6, title6, content6);
        System.out.println("匹配标准案由：" + result6);

        String casereason7 = "[\"金融借款合同纠纷\"]";  // 爬虫字段
        String title7 = "孙鹏、河南兰考农村商业银行股份有限公司等金融借款合同纠纷民事申请再审审查民事裁定书";
        String content7 = "再审申请人（案外人）：孙鹏，男，****年**月**日出生，汉族，住河南省兰考县。 委托诉讼代理人：朱胜利，河南兰桐律师事务所律师，代理权限为特别授权。 被申请人（一审原告）：河南兰考农村商业银行股份有限公司，住所地河南省兰考县。 法定代表人：王全禄。 被申请人（一审被告）：黄海龙，男，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：何涛，男，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：宋永红，女，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：秦某，女，****年**月**日生，汉族，住郑州市。 被申请人（一审被告）：孙某，女，****年**月**日生，汉族，住郑州市。 法定代理人：秦某，女，****年**月**日生，汉族，住郑州市，系孙某之母。 被申请人（一审被告）：孙嘉盟，男，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：孙嘉浩，男，****年**月**日生，汉族，住河南省兰考县。,再审申请人孙鹏因与被申请人河南兰考农村商业银行股份有限公司、黄海龙、何涛、宋永红、秦某、孙某、孙嘉盟、孙嘉浩金融借款合同纠纷一案，不服河南省兰考县人民法院（2020）豫0225民初1752号民事调解书，向本院申请再审。本院依法组成合议庭进行审查。 本院审查过程中，孙鹏以根据《最高人民法院关于适用〈中华人民共和国民事诉讼法〉的解释》第四百二十三条的规定，本案应向兰考县人民法院申请再审为由，向本院提出撤回再审申请。";

        String result7 = matcher.evaluate(casereason7, title7, content7);
        System.out.println("匹配标准案由：" + result7);


    }

}